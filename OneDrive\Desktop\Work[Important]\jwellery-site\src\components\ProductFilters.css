/* Filter Bar Styles */
.filter-bar-container {
  background: white;
  border-bottom: 1px solid #e5e5e5;
  padding: 12px 0;
  margin-bottom: 20px;
}

.filter-bar-content {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.filter-toggle-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  color: #333;
  transition: all 0.2s ease;
}

.filter-toggle-btn:hover {
  border-color: #d4af37;
  background-color: #faf9f7;
}

.filter-toggle-btn i:first-child {
  font-size: 16px;
}

.filter-toggle-btn i:last-child {
  font-size: 12px;
}

.filter-chips {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
  flex: 1;
}

.filter-chip {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  background: #f8f8f8;
  border: 1px solid #ddd;
  border-radius: 20px;
  font-size: 13px;
  color: #333;
}

.filter-chip.price-chip {
  background: #fff;
  border-color: #d4af37;
  color: #d4af37;
}

.chip-remove-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  color: #666;
  transition: all 0.2s ease;
}

.chip-remove-btn:hover {
  background: #e5e5e5;
  color: #333;
}

.show-more-btn {
  background: none;
  border: none;
  color: #d4af37;
  font-size: 13px;
  cursor: pointer;
  text-decoration: underline;
  padding: 0;
}

.show-more-btn:hover {
  color: #b8941f;
}

.sort-section {
  display: flex;
  align-items: center;
  gap: 8px;
  position: relative;
  margin-left: auto;
}

.sort-label {
  font-size: 14px;
  color: #666;
  white-space: nowrap;
}

.sort-select {
  appearance: none;
  background: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 8px 32px 8px 12px;
  font-size: 14px;
  color: #333;
  cursor: pointer;
  min-width: 140px;
}

.sort-select:focus {
  outline: none;
  border-color: #d4af37;
}

.sort-arrow {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  pointer-events: none;
  font-size: 12px;
  color: #666;
}

/* Filter Modal Styles */
.filter-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.filter-modal {
  background: white;
  border-radius: 8px;
  width: 100%;
  max-width: 400px;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.filter-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #e5e5e5;
}

.filter-modal-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.modal-close-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  color: #666;
  transition: all 0.2s ease;
}

.modal-close-btn:hover {
  background: #f5f5f5;
  color: #333;
}

.modal-close-btn i {
  font-size: 20px;
}

.filter-modal-content {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
}

.filter-section {
  margin-bottom: 20px;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 15px;
}

.filter-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.filter-section-header {
  width: 100%;
  background: none;
  border: none;
  padding: 10px 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  font-size: 16px;
  font-weight: 500;
  color: #333;
  text-align: left;
}

.filter-section-header:hover {
  color: #d4af37;
}

.filter-section-header i {
  font-size: 14px;
  transition: transform 0.2s ease;
}

.filter-options {
  padding-top: 10px;
  animation: slideDown 0.2s ease;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.filter-option {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  cursor: pointer;
  font-size: 14px;
  color: #666;
  position: relative;
  padding-left: 25px;
}

.filter-option:last-child {
  margin-bottom: 0;
}

.filter-option input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  left: 0;
  top: 0;
  width: 18px;
  height: 18px;
}

.filter-option input[type="radio"] {
  accent-color: #d4af37;
}

.filter-option input[type="checkbox"] {
  accent-color: #d4af37;
}

.option-label {
  margin-left: 8px;
  user-select: none;
}

.filter-option:hover .option-label {
  color: #333;
}

/* Price Range Styles */
.price-range-inputs {
  display: flex;
  gap: 10px;
  align-items: center;
}

.price-input-group {
  flex: 1;
}

.price-input-group input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  box-sizing: border-box;
}

.price-input-group input:focus {
  outline: none;
  border-color: #d4af37;
  box-shadow: 0 0 0 2px rgba(212, 175, 55, 0.1);
}

.price-separator {
  color: #666;
  font-weight: 500;
}

.price-filter-buttons {
  display: flex;
  gap: 8px;
  margin-top: 12px;
}

.apply-price-btn,
.clear-price-btn {
  flex: 1;
  padding: 8px 16px;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  font-size: 13px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.apply-price-btn {
  background: #d4af37;
  border-color: #d4af37;
  color: white;
}

.apply-price-btn:hover {
  background: #b8941f;
  border-color: #b8941f;
}

.clear-price-btn {
  background: white;
  color: #666;
}

.clear-price-btn:hover {
  border-color: #d4af37;
  background-color: #faf9f7;
  color: #333;
}

.filter-modal-footer {
  display: flex;
  gap: 12px;
  padding: 20px;
  border-top: 1px solid #e5e5e5;
}

.clear-filters-btn {
  flex: 1;
  padding: 12px 20px;
  background: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  color: #333;
  transition: all 0.2s ease;
}

.clear-filters-btn:hover {
  border-color: #d4af37;
  background-color: #faf9f7;
}

.show-results-btn {
  flex: 2;
  padding: 12px 20px;
  background: #8B4513;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  color: white;
  font-weight: 500;
  transition: all 0.2s ease;
}

.show-results-btn:hover {
  background: #7a3a0f;
}

/* Responsive Design */
@media (max-width: 768px) {
  .filter-bar-content {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .sort-section {
    margin-left: 0;
    justify-content: space-between;
  }

  .filter-modal {
    margin: 10px;
    max-height: 90vh;
  }

  .filter-modal-overlay {
    padding: 10px;
  }
}


