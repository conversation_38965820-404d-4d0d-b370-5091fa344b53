'use client';

import React, { useState, useEffect } from 'react';
import { ProductFilters as ProductFiltersType } from '../types';
import { categoryService } from '../lib/firestore';
import './ProductFilters.css';

type SortOption = 'newest' | 'oldest' | 'price-low' | 'price-high' | 'name-asc' | 'name-desc' | 'featured';

interface ProductFiltersProps {
  filters: ProductFiltersType;
  onFilterChange: (filters: ProductFiltersType) => void;
  sortBy: SortOption;
  onSortChange: (sort: SortOption) => void;
}

const ProductFilters: React.FC<ProductFiltersProps> = ({
  filters,
  onFilterChange,
  sortBy,
  onSortChange
}) => {
  const [categories, setCategories] = useState<string[]>([]);
  const [isFilterModalOpen, setIsFilterModalOpen] = useState(false);
  const [tempPriceRange, setTempPriceRange] = useState({
    min: filters.priceRange?.min || '',
    max: filters.priceRange?.max || ''
  });
  const [expandedSections, setExpandedSections] = useState({
    price: true,
    jewelryType: true,
    brand: true,
    gender: true,
    purity: true,
    occasion: true,
    metal: true,
    diamondClarity: true,
    collection: true,
    community: true,
  });

  // Available filter options
  const jewelryTypes = ['Rings', 'Necklaces', 'Earrings', 'Bracelets', 'Bangles', 'Pendants'];
  const brands = ['Tanishq', 'Mia', 'Rivaah', 'Zoya'];
  const genders = ['Women', 'Men', 'Kids'];
  const purities = ['14K', '18K', '22K', '24K'];
  const occasions = ['Daily Wear', 'Wedding', 'Party', 'Festival', 'Office'];
  const metals = ['Gold', 'Silver', 'Platinum', 'Rose Gold'];
  const diamondClarities = ['FL', 'IF', 'VVS1', 'VVS2', 'VS1', 'VS2'];
  const collections = ['Classic', 'Contemporary', 'Traditional', 'Bridal'];
  const communities = ['South Indian', 'North Indian', 'Bengali', 'Gujarati'];

  useEffect(() => {
    loadCategories();
  }, []);

  // Sync temp price range with actual filters
  useEffect(() => {
    setTempPriceRange({
      min: filters.priceRange?.min || '',
      max: filters.priceRange?.max || ''
    });
  }, [filters.priceRange]);

  const loadCategories = async () => {
    try {
      const categoriesData = await categoryService.getCategories();
      setCategories(categoriesData.map(cat => cat.name));
    } catch (error) {
      console.error('Error loading categories:', error);
    }
  };

  const handleCategoryChange = (category: string) => {
    const newFilters = { ...filters };
    if (newFilters.category === category) {
      delete newFilters.category;
    } else {
      newFilters.category = category;
    }
    onFilterChange(newFilters);
  };

  const handleTagChange = (tag: string) => {
    const newFilters = { ...filters };
    const currentTags = newFilters.tags || [];
    
    if (currentTags.includes(tag)) {
      newFilters.tags = currentTags.filter(t => t !== tag);
    } else {
      newFilters.tags = [...currentTags, tag];
    }
    
    if (newFilters.tags.length === 0) {
      delete newFilters.tags;
    }
    
    onFilterChange(newFilters);
  };

  const handleMaterialChange = (material: string) => {
    const newFilters = { ...filters };
    if (newFilters.material === material) {
      delete newFilters.material;
    } else {
      newFilters.material = material;
    }
    onFilterChange(newFilters);
  };

  const handlePriceChange = (min: number, max: number) => {
    const newFilters = { ...filters };
    if (min === 0 && max === 100000) {
      delete newFilters.priceRange;
    } else {
      newFilters.priceRange = { min, max };
    }
    onFilterChange(newFilters);
  };

  const handleTempPriceChange = (field: 'min' | 'max', value: string) => {
    setTempPriceRange(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handlePriceKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      applyPriceFilter();
    }
  };

  const applyPriceFilter = () => {
    const min = parseInt(tempPriceRange.min.toString()) || 0;
    const max = parseInt(tempPriceRange.max.toString()) || 100000;
    handlePriceChange(min, max);
  };

  const clearPriceFilter = () => {
    setTempPriceRange({ min: '', max: '' });
    handlePriceChange(0, 100000);
  };

  const handleAvailabilityChange = (inStock: boolean) => {
    const newFilters = { ...filters };
    if (newFilters.inStock === inStock) {
      delete newFilters.inStock;
    } else {
      newFilters.inStock = inStock;
    }
    onFilterChange(newFilters);
  };

  const toggleSection = (section: keyof typeof expandedSections) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  const clearAllFilters = () => {
    onFilterChange({});
  };

  const hasActiveFilters = Object.keys(filters).length > 0;

  // Get active filter chips
  const getActiveFilterChips = () => {
    const chips = [];

    if (filters.priceRange) {
      chips.push({
        label: `₹${filters.priceRange.min.toLocaleString()} - ₹${filters.priceRange.max.toLocaleString()}`,
        onRemove: () => {
          const newFilters = { ...filters };
          delete newFilters.priceRange;
          onFilterChange(newFilters);
        }
      });
    }

    if (filters.category) {
      chips.push({
        label: `Gifts For ${filters.category}`,
        onRemove: () => {
          const newFilters = { ...filters };
          delete newFilters.category;
          onFilterChange(newFilters);
        }
      });
    }

    if (filters.tags) {
      filters.tags.forEach(tag => {
        chips.push({
          label: tag,
          onRemove: () => handleTagChange(tag)
        });
      });
    }

    return chips;
  };

  const activeChips = getActiveFilterChips();

  return (
    <>
      {/* Filter Bar */}
      <div className="filter-bar-container">
        <div className="filter-bar-content">
          {/* Filter Button */}
          <button
            className="filter-toggle-btn"
            onClick={() => setIsFilterModalOpen(true)}
          >
            <i className="bi bi-funnel"></i>
            Filter
            <i className="bi bi-chevron-down"></i>
          </button>

          {/* Active Filter Chips */}
          <div className="filter-chips">
            {activeChips.map((chip, index) => (
              <div key={index} className="filter-chip">
                <span>{chip.label}</span>
                <button
                  className="chip-remove-btn"
                  onClick={chip.onRemove}
                >
                  <i className="bi bi-x"></i>
                </button>
              </div>
            ))}

            {/* Show More Button */}
            {activeChips.length > 3 && (
              <button className="show-more-btn">
                +Show More
              </button>
            )}
          </div>

          {/* Sort Dropdown */}
          <div className="sort-section">
            <span className="sort-label">Sort By:</span>
            <select
              value={sortBy}
              onChange={(e) => onSortChange(e.target.value as SortOption)}
              className="sort-select"
            >
              <option value="featured">Best Matches</option>
              <option value="newest">Newest First</option>
              <option value="oldest">Oldest First</option>
              <option value="price-low">Price: Low to High</option>
              <option value="price-high">Price: High to Low</option>
              <option value="name-asc">Name: A to Z</option>
              <option value="name-desc">Name: Z to A</option>
            </select>
            <i className="bi bi-chevron-down sort-arrow"></i>
          </div>
        </div>
      </div>

      {/* Filter Modal */}
      {isFilterModalOpen && (
        <div className="filter-modal-overlay" onClick={() => setIsFilterModalOpen(false)}>
          <div className="filter-modal" onClick={(e) => e.stopPropagation()}>
            <div className="filter-modal-header">
              <h3>Filter By</h3>
              <button
                className="modal-close-btn"
                onClick={() => setIsFilterModalOpen(false)}
              >
                <i className="bi bi-x"></i>
              </button>
            </div>

            <div className="filter-modal-content">
              {/* Price Filter */}
              <div className="filter-section">
                <button
                  className="filter-section-header"
                  onClick={() => toggleSection('price')}
                >
                  <span>Price</span>
                  <i className={`bi bi-chevron-${expandedSections.price ? 'up' : 'down'}`}></i>
                </button>

                {expandedSections.price && (
                  <div className="filter-options">
                    <div className="price-range-inputs">
                      <div className="price-input-group">
                        <input
                          type="number"
                          value={tempPriceRange.min}
                          onChange={(e) => handleTempPriceChange('min', e.target.value)}
                          onKeyPress={handlePriceKeyPress}
                          placeholder="Min"
                        />
                      </div>
                      <span className="price-separator">-</span>
                      <div className="price-input-group">
                        <input
                          type="number"
                          value={tempPriceRange.max}
                          onChange={(e) => handleTempPriceChange('max', e.target.value)}
                          onKeyPress={handlePriceKeyPress}
                          placeholder="Max"
                        />
                      </div>
                    </div>
                    <div className="price-filter-buttons">
                      <button
                        className="apply-price-btn"
                        onClick={applyPriceFilter}
                      >
                        Apply
                      </button>
                      <button
                        className="clear-price-btn"
                        onClick={clearPriceFilter}
                      >
                        Clear
                      </button>
                    </div>
                  </div>
                )}
              </div>

              {/* Jewellery Type Filter */}
              <div className="filter-section">
                <button
                  className="filter-section-header"
                  onClick={() => toggleSection('jewelryType')}
                >
                  <span>Jewellery Type</span>
                  <i className={`bi bi-chevron-${expandedSections.jewelryType ? 'up' : 'down'}`}></i>
                </button>

                {expandedSections.jewelryType && (
                  <div className="filter-options">
                    {jewelryTypes.map((type) => (
                      <label key={type} className="filter-option">
                        <input
                          type="radio"
                          name="jewelryType"
                          checked={filters.category === type}
                          onChange={() => handleCategoryChange(type)}
                        />
                        <span className="option-label">{type}</span>
                      </label>
                    ))}
                  </div>
                )}
              </div>

              {/* Brand Filter */}
              <div className="filter-section">
                <button
                  className="filter-section-header"
                  onClick={() => toggleSection('brand')}
                >
                  <span>Brand</span>
                  <i className={`bi bi-chevron-${expandedSections.brand ? 'up' : 'down'}`}></i>
                </button>

                {expandedSections.brand && (
                  <div className="filter-options">
                    {brands.map((brand) => (
                      <label key={brand} className="filter-option">
                        <input
                          type="radio"
                          name="brand"
                          checked={filters.material === brand}
                          onChange={() => handleMaterialChange(brand)}
                        />
                        <span className="option-label">{brand}</span>
                      </label>
                    ))}
                  </div>
                )}
              </div>

              {/* Gender Filter */}
              <div className="filter-section">
                <button
                  className="filter-section-header"
                  onClick={() => toggleSection('gender')}
                >
                  <span>Gender</span>
                  <i className={`bi bi-chevron-${expandedSections.gender ? 'up' : 'down'}`}></i>
                </button>

                {expandedSections.gender && (
                  <div className="filter-options">
                    {genders.map((gender) => (
                      <label key={gender} className="filter-option">
                        <input
                          type="radio"
                          name="gender"
                          checked={filters.tags?.includes(gender.toLowerCase()) || false}
                          onChange={() => handleTagChange(gender.toLowerCase())}
                        />
                        <span className="option-label">{gender}</span>
                      </label>
                    ))}
                  </div>
                )}
              </div>

              {/* More filter sections following the same pattern */}
              {/* Purity, Occasion, Metal, Diamond Clarity, Collection, Community */}

            </div>

            <div className="filter-modal-footer">
              <button
                className="clear-filters-btn"
                onClick={clearAllFilters}
              >
                Clear Filters
              </button>
              <button
                className="show-results-btn"
                onClick={() => setIsFilterModalOpen(false)}
              >
                Show Result (6,011)
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default ProductFilters;
