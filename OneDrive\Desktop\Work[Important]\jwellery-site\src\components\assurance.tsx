import React from 'react';
import '../style/assurance.css';

const Assurance: React.FC = () => {
  return (
    <section className="assurance">
      <div className="assurance-container">
        <div className="assurance-content">
          <div className="assurance-text">
            <h3 className="assurance-title">
              <PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON> <span className="assurance-highlight">Assurance</span>
            </h3>
            <p className="assurance-subtitle">Crafted by experts, cherished by you</p>
          </div>
          
          <div className="assurance-features">
            <div className="assurance-feature">
              <div className="feature-icon">
                <svg width="40" height="40" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M12 2L15.09 8.26L22 9L17 14L18.18 21L12 17.77L5.82 21L7 14L2 9L8.91 8.26L12 2Z" fill="#D4AF37"/>
                </svg>
              </div>
              <div className="feature-text">
                <span className="feature-label">Quality</span>
                <span className="feature-description">Craftsmanship</span>
              </div>
            </div>
            
            <div className="assurance-feature">
              <div className="feature-icon">
                <svg width="40" height="40" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M12 21.35L10.55 20.03C5.4 15.36 2 12.28 2 8.5C2 5.42 4.42 3 7.5 3C9.24 3 10.91 3.81 12 5.09C13.09 3.81 14.76 3 16.5 3C19.58 3 22 5.42 22 8.5C22 12.28 18.6 15.36 13.45 20.04L12 21.35Z" fill="#D4AF37"/>
                </svg>
              </div>
              <div className="feature-text">
                <span className="feature-label">Ethically</span>
                <span className="feature-description">Sourced</span>
              </div>
            </div>
            
            <div className="assurance-feature">
              <div className="feature-icon">
                <svg width="40" height="40" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1L13.5 2.5L16.17 5.17L10.5 10.84L11.92 12.25L15.92 8.25L18.5 10.84L21 9ZM1 9L3.5 10.84L6.08 8.25L10.08 12.25L11.5 10.84L5.83 5.17L8.5 2.5L7 1L1 7V9ZM12 13C10.89 13 10 13.89 10 15V22H14V15C14 13.89 13.11 13 12 13Z" fill="#D4AF37"/>
                </svg>
              </div>
              <div className="feature-text">
                <span className="feature-label">100%</span>
                <span className="feature-description">Transparency</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Assurance;
