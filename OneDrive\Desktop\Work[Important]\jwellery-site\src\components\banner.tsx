"use client";

import React, { useState, useEffect, useRef } from "react";
import '../style/banner.css'

function Banner() {
  const [slideIndex, setSlideIndex] = useState(1);
  const totalSlides = 4;
  const [isTransitioning, setIsTransitioning] = useState(true);
  const [isMobile, setIsMobile] = useState(false);
  const autoSlideInterval = useRef<NodeJS.Timeout | null>(null);
  const startX = useRef(0);
  const endX = useRef(0);

  const changeSlide = (direction: number) => {
    setIsTransitioning(true);
    setSlideIndex(prevIndex => {
      if (direction > 0) {
        // Moving forward
        return prevIndex + 1;
      } else {
        // Moving backward
        return prevIndex - 1;
      }
    });
    restartAutoSlide();
  };

  const currentSlide = (index: number) => {
    setIsTransitioning(true);
    setSlideIndex(index);
    restartAutoSlide();
  };

  const handleTransitionEnd = () => {
    if (slideIndex === totalSlides + 1) {
      // After sliding to duplicate first slide, instantly jump to real first slide
      setIsTransitioning(false);
      setTimeout(() => setSlideIndex(1), 10);
    } else if (slideIndex === 0) {
      // After sliding to duplicate last slide, instantly jump to real last slide
      setIsTransitioning(false);
      setTimeout(() => setSlideIndex(totalSlides), 10);
    }
  };

  const handleImageClick = (imageNumber: number) => {
    stopAutoSlide();

    switch(imageNumber) {
      case 1:
        alert('Banner 1 clicked! You can redirect to a specific page or product.');
        // Example: window.location.href = '/products/collection1';
        break;
      case 2:
        alert('Banner 2 clicked! Add your custom action here.');
        // Example: window.location.href = '/products/collection2';
        break;
      case 3:
        alert('Banner 3 clicked! Add your custom action here.');
        // Example: window.location.href = '/products/collection3';
        break;
      case 4:
        alert('Banner 4 clicked! Add your custom action here.');
        // Example: window.location.href = '/products/collection4';
        break;
      default:
        console.log('Image clicked:', imageNumber);
    }

    setTimeout(startAutoSlide, 3000);
  };

  const autoSlide = () => {
    changeSlide(1);
  };

  const startAutoSlide = () => {
    if (autoSlideInterval.current) {
      clearInterval(autoSlideInterval.current);
    }
    autoSlideInterval.current = setInterval(autoSlide, 4000);
  };

  const stopAutoSlide = () => {
    if (autoSlideInterval.current) {
      clearInterval(autoSlideInterval.current);
      autoSlideInterval.current = null;
    }
  };

  const restartAutoSlide = () => {
    stopAutoSlide();
    startAutoSlide();
  };

  const handleKeyDown = (event: KeyboardEvent) => {
    if (event.key === 'ArrowLeft') {
      changeSlide(-1);
    } else if (event.key === 'ArrowRight') {
      changeSlide(1);
    }
  };

  const handleTouchStart = (event: React.TouchEvent) => {
    startX.current = event.touches[0].clientX;
  };

  const handleTouchEnd = (event: React.TouchEvent) => {
    endX.current = event.changedTouches[0].clientX;
    handleSwipe();
  };

  const handleSwipe = () => {
    const swipeThreshold = 50;
    const diff = startX.current - endX.current;

    if (Math.abs(diff) > swipeThreshold) {
      if (diff > 0) {
        changeSlide(1);
      } else {
        changeSlide(-1);
      }
    }
  };

  useEffect(() => {
    // Check if mobile on mount and window resize
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);

    startAutoSlide();
    document.addEventListener('keydown', handleKeyDown);

    return () => {
      stopAutoSlide();
      document.removeEventListener('keydown', handleKeyDown);
      window.removeEventListener('resize', checkMobile);
    };
  }, []);

  // Handle the instant repositioning after transition ends
  useEffect(() => {
    if (!isTransitioning) {
      const timer = setTimeout(() => {
        setIsTransitioning(true);
      }, 50);
      return () => clearTimeout(timer);
    }
  }, [isTransitioning]);

  // Function to get appropriate image source based on screen size
  const getImageSrc = (imageNumber: number) => {
    const prefix = isMobile ? 'small-banner' : 'banner-image';
    return `/banners/${prefix}-${imageNumber}.png`;
  };

  // Calculate translateX for infinite loop (6 slides total: last + 4 original + first)
  // slideIndex 1 = real first slide (position 1 in the array)
  const translateX = -slideIndex * 16.666667;

  return (
    <div className="slideshow-container" onTouchStart={handleTouchStart} onTouchEnd={handleTouchEnd}>
      <div
        className="slides-wrapper"
        style={{
          transform: `translateX(${translateX}%)`,
          transition: isTransitioning ? 'transform 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94)' : 'none'
        }}
        onTransitionEnd={handleTransitionEnd}
      >
        {/* Duplicate of last slide for seamless loop */}
        <div className="slide">
          <img src={getImageSrc(4)} alt="Banner 4" onClick={() => handleImageClick(4)} />
        </div>
        {/* Original slides */}
        <div className="slide">
          <img src={getImageSrc(1)} alt="Banner 1" onClick={() => handleImageClick(1)} />
        </div>
        <div className="slide">
          <img src={getImageSrc(2)} alt="Banner 2" onClick={() => handleImageClick(2)} />
        </div>
        <div className="slide">
          <img src={getImageSrc(3)} alt="Banner 3" onClick={() => handleImageClick(3)} />
        </div>
        <div className="slide">
          <img src={getImageSrc(4)} alt="Banner 4" onClick={() => handleImageClick(4)} />
        </div>
        {/* Duplicate of first slide for seamless loop */}
        <div className="slide">
          <img src={getImageSrc(1)} alt="Banner 1" onClick={() => handleImageClick(1)} />
        </div>
      </div>

      <button className="nav-button prev" onClick={() => changeSlide(-1)} suppressHydrationWarning={true}>❮</button>
      <button className="nav-button next" onClick={() => changeSlide(1)} suppressHydrationWarning={true}>❯</button>

      <div className="dots-container">
        {[1, 2, 3, 4].map((index) => {
          // Calculate which dot should be active based on current slide position
          let activeIndex = slideIndex;
          if (slideIndex === 0) activeIndex = 4; // When at duplicate last slide
          if (slideIndex === totalSlides + 1) activeIndex = 1; // When at duplicate first slide

          return (
            <span
              key={index}
              className={`dot ${activeIndex === index ? 'active' : ''}`}
              onClick={() => currentSlide(index)}
            ></span>
          );
        })}
      </div>
    </div>
  );
}

export default Banner;
