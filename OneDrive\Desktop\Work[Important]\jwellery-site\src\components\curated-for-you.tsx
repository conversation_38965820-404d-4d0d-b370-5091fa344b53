import React from 'react';
import '../style/curated-for-you.css';

const CuratedForYou: React.FC = () => {
  return (
    <section className="curated-for-you">
      <div className="curated-container">
        {/* Header Section */}
        <div className="curated-header">
          <h2 className="curated-title">Curated For You</h2>
          <p className="curated-subtitle">Shop by Gender</p>
        </div>

        {/* Category Cards */}
        <div className="category-grid">
          <div className="category-card">
            <div className="category-image">
              <img
                src="/main-images/women-jewellery.jpg"
                alt="Women Jewellery"
                className="category-img"
              />
            </div>
            <div className="category-label">
              <span>Women Jewellery</span>
            </div>
          </div>

          <div className="category-card">
            <div className="category-image">
              <img
                src="/main-images/men-jewellery.jpg"
                alt="Men Jewellery"
                className="category-img"
              />
            </div>
            <div className="category-label">
              <span>Men Jewellery</span>
            </div>
          </div>

          <div className="category-card">
            <div className="category-image">
              <img
                src="/main-images/kids-jewellery.jpg"
                alt="Kids Jewellery"
                className="category-img"
              />
            </div>
            <div className="category-label">
              <span>Kids Jewellery</span>
            </div>
          </div>
        </div>

        {/* Bottom Section with Gift and Exchange */}
        <div className="bottom-section">
          {/* Gift of Choice Card */}
          <div className="gift-card">
            <div className="gift-ribbon">
              <div className="ribbon-vertical"></div>
              <div className="ribbon-horizontal"></div>
              <div className="ribbon-bow"></div>
            </div>
            <div className="gift-content">
              <h3 className="gift-hashtag">#GiftOfChoice</h3>
              <p className="gift-description">
                Breathtaking gifts for your loved one's.<br />
                <strong>STARTING AT ₹10,000</strong>
              </p>
              <button className="gift-button" suppressHydrationWarning={true}>
                Explore Now
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M5 12H19M19 12L12 5M19 12L12 19" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </button>
            </div>
          </div>

          {/* Exchange Program Card */}
          <div className="exchange-card">
            <div className="exchange-icon">
              <svg width="60" height="60" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 2L13.09 8.26L22 9L17 14L18.18 21L12 17.77L5.82 21L7 14L2 9L8.91 8.26L12 2Z" fill="#D4AF37"/>
              </svg>
            </div>
            <div className="exchange-content">
              <h3 className="exchange-title">Exchange your Old Gold for 100% Value!</h3>
              <p className="exchange-description">
                Unlock full value for your old gold today with<br />
                our <strong>Exchange Program !</strong>
              </p>
              <button className="exchange-button" suppressHydrationWarning={true}>
                Know more
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M5 12H19M19 12L12 5M19 12L12 19" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default CuratedForYou;
