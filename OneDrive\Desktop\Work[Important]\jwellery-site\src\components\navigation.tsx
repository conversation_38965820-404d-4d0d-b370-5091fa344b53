"use client"

import React, { useEffect } from 'react'
import '../style/navigation.css'

function Navigation() {
  useEffect(() => {
    function performSearchInternal() {
      const searchInput = document.getElementById('search-input') as HTMLInputElement;
      const searchTerm = searchInput?.value.trim();

      if (searchTerm) {
        // You can modify this to redirect to your search page
        alert('Searching for: ' + searchTerm);
        // Example: window.location.href = '/search?q=' + encodeURIComponent(searchTerm);
      } else {
        alert('Please enter a search term');
      }
    }

    // Handle search icon click in scrolled mode
    function handleSearchIconClick(event: Event) {
      const searchBar = document.querySelector('.search-bar') as HTMLElement;
      const expandedSearchBar = document.querySelector('.expanded-search-bar') as HTMLElement;

      if (searchBar?.classList.contains('scrolled')) {
        event.stopPropagation();

        // Store current scroll position and prevent scrolling
        const scrollY = window.scrollY;
        document.body.style.top = `-${scrollY}px`;
        document.body.classList.add('no-scroll');

        expandedSearchBar?.classList.add('active');
        // Focus the input after a small delay to ensure it's visible
        setTimeout(() => {
          const expandedInput = document.getElementById('expanded-search-input') as HTMLInputElement;
          expandedInput?.focus();
        }, 100);
      }
    }

    // Handle click outside to collapse search bar
    function handleClickOutside(event: Event) {
      const expandedSearchBar = document.querySelector('.expanded-search-bar') as HTMLElement;
      const expandedSearchContent = document.querySelector('.expanded-search-content') as HTMLElement;
      const target = event.target as HTMLElement;

      if (expandedSearchBar?.classList.contains('active')) {
        // Check if click is outside the search content area (but allow clicking on the blur background)
        if (!expandedSearchContent?.contains(target)) {
          // Restore scrolling
          const scrollY = document.body.style.top;
          document.body.classList.remove('no-scroll');
          document.body.style.top = '';
          if (scrollY) {
            window.scrollTo(0, parseInt(scrollY || '0') * -1);
          }

          expandedSearchBar.classList.remove('active');
        }
      }
    }

    // Handle scroll behavior for search bar - Mobile only
    function handleScroll() {
      const searchBar = document.querySelector('.search-bar') as HTMLElement;
      const expandedSearchBar = document.querySelector('.expanded-search-bar') as HTMLElement;
      const currentScroll = window.pageYOffset || document.documentElement.scrollTop;

      // Only enable scroll behavior on mobile (768px and below)
      if (window.innerWidth <= 768) {
        if (currentScroll > 100) {
          searchBar?.classList.add('scrolled');
        } else {
          searchBar?.classList.remove('scrolled');
          // Hide expanded search bar and restore scrolling when scrolling back up
          if (expandedSearchBar?.classList.contains('active')) {
            const scrollY = document.body.style.top;
            document.body.classList.remove('no-scroll');
            document.body.style.top = '';
            expandedSearchBar.classList.remove('active');
          }
        }
      }
    }

    // Handle window resize
    function handleResize() {
      const searchBar = document.querySelector('.search-bar') as HTMLElement;
      const expandedSearchBar = document.querySelector('.expanded-search-bar') as HTMLElement;

      if (window.innerWidth > 768) {
        // Remove scrolled class, hide expanded search, and restore scrolling on desktop
        searchBar?.classList.remove('scrolled');
        if (expandedSearchBar?.classList.contains('active')) {
          const scrollY = document.body.style.top;
          document.body.classList.remove('no-scroll');
          document.body.style.top = '';
          if (scrollY) {
            window.scrollTo(0, parseInt(scrollY || '0') * -1);
          }
          expandedSearchBar.classList.remove('active');
        }
      } else {
        // Re-check scroll position on mobile
        handleScroll();
      }
    }

    // Add event listeners
    window.addEventListener('scroll', handleScroll);
    window.addEventListener('resize', handleResize);
    document.addEventListener('click', handleClickOutside);

    // Add click listener to search icon
    const searchIcon = document.querySelector('.search-bar img') as HTMLElement;
    searchIcon?.addEventListener('click', handleSearchIconClick);

    // Cleanup
    return () => {
      window.removeEventListener('scroll', handleScroll);
      window.removeEventListener('resize', handleResize);
      document.removeEventListener('click', handleClickOutside);
      searchIcon?.removeEventListener('click', handleSearchIconClick);
    };
  }, []);

  const handleSearchKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {
    if (event.key === 'Enter') {
      performSearch();
    }
  };

  const performSearch = () => {
    const searchInput = document.getElementById('search-input') as HTMLInputElement;
    const expandedSearchInput = document.getElementById('expanded-search-input') as HTMLInputElement;
    const expandedSearchBar = document.querySelector('.expanded-search-bar') as HTMLElement;
    const searchBar = document.querySelector('.search-bar') as HTMLElement;

    // Determine which input to use
    let searchTerm = '';
    if (expandedSearchBar?.classList.contains('active')) {
      searchTerm = expandedSearchInput?.value.trim() || '';
    } else {
      searchTerm = searchInput?.value.trim() || '';
    }

    // If in scrolled mode and expanded search is not active, show expanded search instead
    if (searchBar?.classList.contains('scrolled') && !expandedSearchBar?.classList.contains('active')) {
      expandedSearchBar?.classList.add('active');
      setTimeout(() => {
        expandedSearchInput?.focus();
      }, 100);
      return;
    }

    if (searchTerm) {
      // You can modify this to redirect to your search page
      alert('Searching for: ' + searchTerm);
      // Example: window.location.href = '/search?q=' + encodeURIComponent(searchTerm);
      // Hide expanded search after search
      expandedSearchBar?.classList.remove('active');
    } else {
      alert('Please enter a search term');
    }
  };

  const performExpandedSearch = () => {
    const expandedSearchInput = document.getElementById('expanded-search-input') as HTMLInputElement;
    const expandedSearchBar = document.querySelector('.expanded-search-bar') as HTMLElement;
    const searchTerm = expandedSearchInput?.value.trim();

    if (searchTerm) {
      // You can modify this to redirect to your search page
      alert('Searching for: ' + searchTerm);
      // Example: window.location.href = '/search?q=' + encodeURIComponent(searchTerm);

      // Restore scrolling and hide expanded search after search
      const scrollY = document.body.style.top;
      document.body.classList.remove('no-scroll');
      document.body.style.top = '';
      if (scrollY) {
        window.scrollTo(0, parseInt(scrollY || '0') * -1);
      }
      expandedSearchBar?.classList.remove('active');
    } else {
      alert('Please enter a search term');
    }
  };

  const handleExpandedSearchKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {
    if (event.key === 'Enter') {
      performExpandedSearch();
    }
  };

  const toggleMenu = () => {
    const hamburger = document.querySelector('.hamburger');
    const menu = document.getElementById('category-menu');

    hamburger?.classList.toggle('active');
    menu?.classList.toggle('active');
  };

  const handleMenuKeyDown = (event: React.KeyboardEvent<HTMLDivElement>) => {
    if (event.key === 'Enter') {
      toggleMenu();
    }
  };

  useEffect(() => {
    // Close menu when clicking outside
    const handleClickOutside = (event: MouseEvent) => {
      const menuContent = document.querySelector('.menu-content');
      const hamburger = document.querySelector('.hamburger');
      const menu = document.getElementById('category-menu');
      const target = event.target as Node;

      // Check if click is outside menu content and not on hamburger
      if (menu?.classList.contains('active') &&
          menuContent &&
          !menuContent.contains(target) &&
          !hamburger?.contains(target)) {
        hamburger?.classList.remove('active');
        menu?.classList.remove('active');
      }
    };

    // Close menu when clicking on the overlay background
    const handleOverlayClick = (event: MouseEvent) => {
      const target = event.target as HTMLElement;
      if (target.classList.contains('category-menu')) {
        const hamburger = document.querySelector('.hamburger');
        const menu = document.getElementById('category-menu');
        hamburger?.classList.remove('active');
        menu?.classList.remove('active');
      }
    };

    document.addEventListener('click', handleClickOutside);
    document.addEventListener('click', handleOverlayClick);

    return () => {
      document.removeEventListener('click', handleClickOutside);
      document.removeEventListener('click', handleOverlayClick);
    };
  }, []);

  return (
    <>
      <div className='navigation'>
        <div className='logo'>
          <h1>Radhe-Radhe</h1>
        </div>
        <div className='search-bar'>
          <input
            type="text"
            placeholder="search for products, brands, and more..."
            id="search-input"
            onKeyDown={handleSearchKeyDown}
            suppressHydrationWarning={true}
          />
          <img
            src="/images/image1.png"
            onClick={performSearch}
            alt="Search"
            style={{ cursor: 'pointer' }}
          />
        </div>
        <div className="custom-image">
          <img src="/images/image2.png" alt="Custom Image" id="diamond" />
          <img src="/images/image3.png" alt="Custom Image" id="shop" />
          <img src="/images/image4.png" alt="Custom Image" id="wishlist" />
          <div className="login-dropdown">
            <img src="/images/image5.png" alt="Custom Image" id="login-image" />
            <div className="dropdown-content">
              <a href="#" id="login">Login/sign up</a>
              <a href="#" id="contact">Contact us</a>
            </div>
          </div>
        </div>
      </div>

      {/* Expanded search bar for mobile scroll mode */}
      <div className="expanded-search-bar">
        <div className="expanded-search-content">
          <input
            type="text"
            placeholder="search for products, brands, and more..."
            id="expanded-search-input"
            onKeyDown={handleExpandedSearchKeyDown}
            suppressHydrationWarning={true}
          />
          <img
            src="/images/image1.png"
            onClick={performExpandedSearch}
            alt="Search"
            style={{ cursor: 'pointer' }}
          />
        </div>
      </div>

      <div className="menu-container">
        <div className="hamburger" onClick={toggleMenu} onKeyDown={handleMenuKeyDown} tabIndex={0}>
          <span></span>
          <span></span>
          <span></span>
        </div>

        <div className="category-menu" id="category-menu">
          <div className="menu-content">
            <a href="#" className="menu-item">
              <img src="/images/icons8-jewelry-50.png" alt="Jewelry" />
              All Jewelerry
              <img src="/images/icons8-chevron-30.png" alt="Chevron" className="chevron" />
            </a>
            <a href="#" className="menu-item">
              <img src="/images/icons8-gold-50.png" alt="Gold" />
              Gold
              <img src="/images/icons8-chevron-30.png" alt="Chevron" className="chevron" />
            </a>
            <a href="#" className="menu-item">
              <img src="/images/icons8-diamond-50 (1).png" alt="Diamond" />
              Diamond
              <img src="/images/icons8-chevron-30.png" alt="Chevron" className="chevron" />
            </a>
            <a href="#" className="menu-item">
              <img src="/images/icons8-earrings-50.png" alt="Earrings" />
              Earrings
              <img src="/images/icons8-chevron-30.png" alt="Chevron" className="chevron" />
            </a>
            <a href="#" className="menu-item">
              <img src="/images/icons8-diamond-ring-50.png" alt="Rings" />
              Rings
              <img src="/images/icons8-chevron-30.png" alt="Chevron" className="chevron" />
            </a>
            <a href="#" className="menu-item">
              <img src="/images/icons8-collection-50.png" alt="Collections" />
              Collections
              <img src="/images/icons8-chevron-30.png" alt="Chevron" className="chevron" />
            </a>
            <a href="#" className="menu-item">
              <img src="/images/icons8-wedding-50.png" alt="Wedding" />
              Wedding
              <img src="/images/icons8-chevron-30.png" alt="Chevron" className="chevron" />
            </a>
            <a href="#" className="menu-item">
              <img src="/images/icons8-gift-50.png" alt="Gifting" />
              Gifting
              <img src="/images/icons8-chevron-30.png" alt="Chevron" className="chevron" />
            </a>
          </div>
        </div>
      </div>
    </>
  )
}

export default Navigation