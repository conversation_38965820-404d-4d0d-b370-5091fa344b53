.assurance {
  background: linear-gradient(135deg, #f8f6f0 0%, #f0ede5 50%, #e8e3d8 100%);
  padding: 80px 0;
  position: relative;
}

.assurance-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.assurance-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 60px;
}

.assurance-text {
  flex: 1;
}

.assurance-title {
  font-size: 2.5rem;
  font-weight: 300;
  margin: 0 0 12px 0;
  color: #2c2c2c;
  letter-spacing: 1px;
}

.assurance-highlight {
  color: #D4AF37;
  font-weight: 400;
}

.assurance-subtitle {
  font-size: 1.1rem;
  color: #666;
  margin: 0;
  font-weight: 300;
  font-style: italic;
}

.assurance-features {
  display: flex;
  gap: 60px;
  flex: 2;
  justify-content: flex-end;
}

.assurance-feature {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  gap: 15px;
}

.feature-icon {
  width: 70px;
  height: 70px;
  background: rgba(212, 175, 55, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  border: 2px solid rgba(212, 175, 55, 0.2);
  box-shadow: 0 4px 15px rgba(212, 175, 55, 0.1);
}

.feature-icon:hover {
  background: rgba(212, 175, 55, 0.2);
  border-color: #D4AF37;
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(212, 175, 55, 0.2);
}

.feature-text {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.feature-label {
  font-size: 1rem;
  font-weight: 600;
  color: #D4AF37;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.feature-description {
  font-size: 0.9rem;
  color: #666;
  font-weight: 400;
}

/* Responsive styles */
@media (max-width: 768px) {
  .assurance {
    padding: 60px 0;
  }
  
  .assurance-content {
    flex-direction: column;
    gap: 40px;
    text-align: center;
  }
  
  .assurance-title {
    font-size: 2rem;
  }
  
  .assurance-features {
    justify-content: center;
    gap: 40px;
  }
  
  .feature-icon {
    width: 60px;
    height: 60px;
  }
  
  .feature-icon svg {
    width: 35px;
    height: 35px;
  }
}

@media (max-width: 480px) {
  .assurance {
    padding: 40px 0;
  }
  
  .assurance-container {
    padding: 0 15px;
  }
  
  .assurance-title {
    font-size: 1.8rem;
  }
  
  .assurance-subtitle {
    font-size: 1rem;
  }
  
  .assurance-features {
    gap: 30px;
  }
  
  .feature-icon {
    width: 55px;
    height: 55px;
  }
  
  .feature-icon svg {
    width: 30px;
    height: 30px;
  }
  
  .feature-label {
    font-size: 0.9rem;
  }
  
  .feature-description {
    font-size: 0.8rem;
  }
}
