.curated-for-you {
  background: #ffffff;
  padding: 80px 0;
}

.curated-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Header Section */
.curated-header {
  text-align: center;
  margin-bottom: 50px;
}

.curated-title {
  font-size: 2.5rem;
  font-weight: 400;
  color: #2c2c2c;
  margin: 0 0 8px 0;
  letter-spacing: 1px;
}

.curated-subtitle {
  font-size: 1.1rem;
  color: #666;
  margin: 0;
  font-weight: 300;
}

/* Category Grid */
.category-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30px;
  margin-bottom: 60px;
}

.category-card {
  position: relative;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  cursor: pointer;
}

.category-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.15);
}

.category-image {
  width: 100%;
  height: 250px;
  overflow: hidden;
}

.category-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.category-card:hover .category-img {
  transform: scale(1.05);
}

.category-label {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  padding: 30px 20px 20px;
  color: white;
  text-align: center;
}

.category-label span {
  font-size: 1.2rem;
  font-weight: 500;
  letter-spacing: 0.5px;
}

/* Bottom Section */
.bottom-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
}

/* Gift Card */
.gift-card {
  background: linear-gradient(135deg, #f4e6e6 0%, #e8d5d5 100%);
  border-radius: 15px;
  padding: 40px;
  position: relative;
  overflow: hidden;
}

.gift-ribbon {
  position: absolute;
  top: 0;
  left: 0;
  width: 80px;
  height: 100%;
}

.ribbon-vertical {
  position: absolute;
  left: 20px;
  top: 0;
  width: 8px;
  height: 100%;
  background: #8B4513;
}

.ribbon-horizontal {
  position: absolute;
  left: 0;
  top: 50%;
  width: 100%;
  height: 8px;
  background: #8B4513;
  transform: translateY(-50%);
}

.ribbon-bow {
  position: absolute;
  left: 15px;
  top: 50%;
  width: 18px;
  height: 18px;
  background: #8B4513;
  border-radius: 50%;
  transform: translateY(-50%);
}

.gift-content {
  margin-left: 60px;
}

.gift-hashtag {
  font-size: 1.8rem;
  font-weight: 600;
  color: #8B4513;
  margin: 0 0 15px 0;
}

.gift-description {
  font-size: 1rem;
  color: #666;
  margin: 0 0 25px 0;
  line-height: 1.6;
}

.gift-button {
  background: transparent;
  border: 2px solid #8B4513;
  color: #8B4513;
  padding: 12px 24px;
  border-radius: 25px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
}

.gift-button:hover {
  background: #8B4513;
  color: white;
}

/* Exchange Card */
.exchange-card {
  background: linear-gradient(135deg, #f8f6f0 0%, #f0ede5 100%);
  border-radius: 15px;
  padding: 40px;
  display: flex;
  align-items: center;
  gap: 30px;
}

.exchange-icon {
  flex-shrink: 0;
}

.exchange-content h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #2c2c2c;
  margin: 0 0 15px 0;
  line-height: 1.3;
}

.exchange-description {
  font-size: 1rem;
  color: #666;
  margin: 0 0 25px 0;
  line-height: 1.6;
}

.exchange-button {
  background: transparent;
  border: 2px solid #D4AF37;
  color: #D4AF37;
  padding: 12px 24px;
  border-radius: 25px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
}

.exchange-button:hover {
  background: #D4AF37;
  color: white;
}

/* Responsive Design */
@media (max-width: 768px) {
  .curated-for-you {
    padding: 60px 0;
  }
  
  .curated-title {
    font-size: 2rem;
  }
  
  .category-grid {
    grid-template-columns: 1fr;
    gap: 20px;
    margin-bottom: 40px;
  }
  
  .bottom-section {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .gift-card,
  .exchange-card {
    padding: 30px;
  }
  
  .exchange-card {
    flex-direction: column;
    text-align: center;
    gap: 20px;
  }
  
  .gift-content {
    margin-left: 40px;
  }
}

@media (max-width: 480px) {
  .curated-for-you {
    padding: 40px 0;
  }
  
  .curated-container {
    padding: 0 15px;
  }
  
  .curated-title {
    font-size: 1.8rem;
  }
  
  .gift-card,
  .exchange-card {
    padding: 25px;
  }
  
  .gift-content {
    margin-left: 30px;
  }
  
  .gift-hashtag {
    font-size: 1.5rem;
  }
  
  .exchange-content h3 {
    font-size: 1.3rem;
  }
}
