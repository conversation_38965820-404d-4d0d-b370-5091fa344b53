.footer {
  background: linear-gradient(135deg, #2c2c2c 0%, #1a1a1a 100%);
  color: #ffffff;
  padding: 60px 0 0;
}

.footer-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Main Footer Content */
.footer-content {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr 1.5fr;
  gap: 40px;
  margin-bottom: 50px;
}

.footer-section h4 {
  color: #D4AF37;
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 20px;
  letter-spacing: 0.5px;
}

/* Company Info Section */
.footer-logo h3 {
  color: #D4AF37;
  font-size: 2rem;
  font-weight: 600;
  margin: 0 0 8px 0;
  letter-spacing: 1px;
}

.footer-tagline {
  color: #cccccc;
  font-size: 0.9rem;
  font-style: italic;
  margin: 0 0 20px 0;
}

.footer-description {
  color: #cccccc;
  line-height: 1.6;
  margin-bottom: 25px;
  font-size: 0.95rem;
}

/* Social Links */
.social-links {
  display: flex;
  gap: 15px;
}

.social-link {
  width: 40px;
  height: 40px;
  background: rgba(212, 175, 55, 0.1);
  border: 1px solid rgba(212, 175, 55, 0.3);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #D4AF37;
  transition: all 0.3s ease;
  text-decoration: none;
}

.social-link:hover {
  background: #D4AF37;
  color: #2c2c2c;
  transform: translateY(-2px);
}

/* Footer Links */
.footer-links {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-links li {
  margin-bottom: 12px;
}

.footer-links a {
  color: #cccccc;
  text-decoration: none;
  font-size: 0.95rem;
  transition: color 0.3s ease;
}

.footer-links a:hover {
  color: #D4AF37;
}

/* Contact Info */
.contact-info {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 12px;
  color: #cccccc;
  font-size: 0.95rem;
}

.contact-item svg {
  color: #D4AF37;
  flex-shrink: 0;
}

/* Newsletter Section */
.newsletter-section {
  background: rgba(212, 175, 55, 0.1);
  border: 1px solid rgba(212, 175, 55, 0.2);
  border-radius: 15px;
  padding: 30px;
  margin-bottom: 40px;
  text-align: center;
}

.newsletter-title {
  color: #D4AF37;
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 10px;
}

.newsletter-description {
  color: #cccccc;
  margin-bottom: 25px;
  line-height: 1.5;
}

.newsletter-form {
  display: flex;
  gap: 10px;
  max-width: 400px;
  margin: 0 auto;
}

.newsletter-input {
  flex: 1;
  padding: 12px 16px;
  border: 1px solid rgba(212, 175, 55, 0.3);
  border-radius: 25px;
  background: rgba(255, 255, 255, 0.1);
  color: #ffffff;
  font-size: 0.95rem;
  outline: none;
  transition: border-color 0.3s ease;
}

.newsletter-input::placeholder {
  color: #999999;
}

.newsletter-input:focus {
  border-color: #D4AF37;
}

.newsletter-button {
  background: #D4AF37;
  color: #2c2c2c;
  border: none;
  padding: 12px 24px;
  border-radius: 25px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.newsletter-button:hover {
  background: #c19b26;
  transform: translateY(-1px);
}

/* Footer Bottom */
.footer-bottom {
  border-top: 1px solid rgba(212, 175, 55, 0.2);
  padding: 25px 0;
}

.footer-bottom-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.copyright {
  color: #999999;
  margin: 0;
  font-size: 0.9rem;
}

.footer-bottom-links {
  display: flex;
  gap: 25px;
}

.footer-bottom-links a {
  color: #999999;
  text-decoration: none;
  font-size: 0.9rem;
  transition: color 0.3s ease;
}

.footer-bottom-links a:hover {
  color: #D4AF37;
}

/* Responsive Design */
@media (max-width: 768px) {
  .footer {
    padding: 40px 0 0;
  }
  
  .footer-content {
    grid-template-columns: 1fr;
    gap: 30px;
    margin-bottom: 30px;
  }
  
  .newsletter-form {
    flex-direction: column;
  }
  
  .footer-bottom-content {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }
  
  .footer-bottom-links {
    gap: 15px;
  }
}

@media (max-width: 480px) {
  .footer-container {
    padding: 0 15px;
  }
  
  .newsletter-section {
    padding: 20px;
  }
  
  .footer-logo h3 {
    font-size: 1.8rem;
  }
  
  .social-links {
    justify-content: center;
  }
  
  .footer-bottom-links {
    flex-direction: column;
    gap: 10px;
  }
}
