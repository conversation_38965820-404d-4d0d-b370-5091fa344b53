/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* Top Navigation Bar */
.top-nav {
    background: linear-gradient(135deg, #d4af37, #ffd700);
    padding: 0.5rem 0;
    position: sticky;
    top: 0;
    z-index: 1001;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.top-nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

.top-nav-links {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 2rem;
    flex-wrap: wrap;
}

.top-nav-link {
    color: #2c3e50;
    text-decoration: none;
    font-weight: 500;
    font-size: 0.9rem;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    transition: all 0.3s ease;
    white-space: nowrap;
}

.top-nav-link:hover {
    background: rgba(255, 255, 255, 0.2);
    color: #1a252f;
    transform: translateY(-1px);
}

/* Hide top nav on mobile */
@media (max-width: 768px) {
    .top-nav {
        display: none;
    }
}


/* Navigation container */
.navigation {
    background: rgb(255, 255, 255);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0);
    padding: 1.5rem 2rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 1rem;
    position: sticky;
    top: 0;
    z-index: 1000;
    min-height: 90px;
}

@media (min-width: 769px) {
    .navigation {
        top: 60px; /* Account for top nav height */
    }
}

/* Logo section */
.logo h1 {
    color: rgb(196, 149, 6);
    font-size: clamp(1.2rem, 4vw, 2rem);
    font-weight: 700;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    letter-spacing: -0.5px;
    cursor: pointer;
    transition: transform 0.3s ease;
    margin: 0;
}

.logo h1:hover {
    transform: scale(1.05);
}

/* Search bar section */
.search-bar {
    display: flex;
    align-items: center;
    background: rgba(255, 255, 255, 0);
    border-radius: 25px;
    padding: 0.5rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    flex: 1;
    max-width: 600px;
    height: 45px;
    margin: 0 1rem;
    border: 1px solid rgb(168, 168, 168);
}

.search-bar input {
    flex: 1;
    color: #000000;
    border: none;
    outline: none;
    padding: 0.75rem 1rem;
    font-size: 1rem;
    border-radius: 20px;
    background: transparent;
}

.search-bar input::placeholder {
    color: #999;
    font-style: italic;
}

.search-bar img {
    width: 20px;
    height: 20px;
    margin: 0 0.5rem;
    cursor: pointer;
    transition: transform 0.2s ease;
}

.search-bar img:hover {
    transform: scale(1.1);
}

/* Scrolled search bar styles - Mobile only */
.search-bar.scrolled {
    display: none; /* Hidden by default on desktop */
}

/* Show scrolled search bar only on mobile */
@media (max-width: 768px) {
    .search-bar.scrolled {
        display: flex !important;
        position: fixed;
        top: 1.5rem;
        right: 4rem;
        width: 36px !important;
        height: 36px !important;
        max-width: 36px !important;
        background: rgba(255, 255, 255, 0.9) !important;
        border: 1px solid rgba(0, 0, 0, 0.1) !important;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1) !important;
        backdrop-filter: blur(10px) !important;
        padding: 0 !important;
        z-index: 1003;
        transition: all 0.3s ease;
        justify-content: center;
        align-items: center;
        border-radius: 50% !important;
    }

    .search-bar.scrolled input {
        display: none;
    }

    .search-bar.scrolled img {
        margin: 0;
        width: 28px !important;
        height: 28px !important;
        opacity: 0.9;
        filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
        transition: all 0.3s ease;
        cursor: pointer;
    }

    .search-bar.scrolled:hover img {
        opacity: 1;
        transform: scale(1.1);
    }
}

/* Expanded search bar for mobile scroll mode */
.expanded-search-bar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1002;
    padding: 1rem;
    transform: translateY(-100%);
    transition: transform 0.3s ease;
    display: none;
}

.expanded-search-bar::before {

    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 150px;
    box-shadow: 0 4px 25px rgba(0, 0, 0, 0.15);
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

/* Show expanded search bar only on mobile */
@media (max-width: 768px) {
    .expanded-search-bar {
        display: block;
    }

    .expanded-search-bar.active {
        transform: translateY(0);
    }
}

.expanded-search-content {
    display: flex;
    align-items: center;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 25px;
    padding: 0.5rem;
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(168, 168, 168, 0.6);
    max-width: 600px;
    margin: 0 auto;
    margin-top: 4rem; /* Space for increased navigation height */
    position: relative;
    z-index: 1003;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.expanded-search-content input {
    flex: 1;
    border: none;
    outline: none;
    padding: 0.75rem 1rem;
    font-size: 1rem;
    border-radius: 20px;
    background: transparent;
    color: #333;
}

.expanded-search-content input::placeholder {
    color: #999;
    font-style: italic;
}

.expanded-search-content img {
    width: 20px;
    height: 20px;
    margin: 0 0.5rem;
    cursor: pointer;
    transition: transform 0.2s ease;
    opacity: 0.8;
}

.expanded-search-content img:hover {
    transform: scale(1.1);
    opacity: 1;
}

/* Responsive styles for expanded search */
@media (max-width: 480px) {
    .expanded-search-content {
        margin-top: 2.5rem;
        padding: 0.4rem;
    }

    .expanded-search-content input {
        padding: 0.6rem 0.8rem;
        font-size: 0.9rem;
    }

    .expanded-search-content img {
        width: 18px;
        height: 18px;
    }
}

@media (max-width: 360px) {
    .expanded-search-content {
        margin-top: 2rem;
        padding: 0.3rem;
    }

    .expanded-search-content input {
        padding: 0.5rem 0.7rem;
        font-size: 0.85rem;
    }

    .expanded-search-content img {
        width: 16px;
        height: 16px;
    }
}

/* Search button styles removed - now using Enter key and icon click */

/* Custom image section */
.custom-image {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.custom-image img {
    width: clamp(24px, 3vw, 32px);
    height: clamp(24px, 3vw, 32px);
    cursor: pointer;
    transition: all 0.3s ease;
    border-radius: 50%;
    padding: 0.25rem;
    background: rgba(255, 255, 255, 0.1);
    min-height: 44px; /* Touch-friendly minimum size */
    min-width: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.custom-image img:hover {
    transform: translateY(-2px);
    background: rgba(255, 255, 255, 0.2);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0);
}

/* Login dropdown section */
.login-dropdown {
    position: relative;
    display: inline-block;
    margin-left: 1rem;
    padding-left: 1rem;
    border-left: 1px solid rgba(0, 0, 0, 0.3);
}

.login-dropdown img {
    width: 32px;
    height: 32px;
    cursor: pointer;
    transition: all 0.3s ease;
    border-radius: 50%;
    padding: 0.25rem;
    background: rgba(255, 255, 255, 0.1);
}

.login-dropdown img:hover {
    transform: translateY(-2px);
    background: rgba(255, 255, 255, 0.2);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* Dropdown content */
.dropdown-content {
    display: none;
    position: absolute;
    right: 0;
    top: 100%;
    background: rgb(255, 255, 255);
    min-width: 180px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    border-radius: 12px;
    z-index: 9999;
    padding: 0.5rem 0;
    margin-top: 0.5rem;
    opacity: 0;
    transform: translateY(-10px);
    transition: all 0.3s ease;
}

.dropdown-content::before {
    content: '';
    position: absolute;
    top: -8px;
    right: 20px;
    width: 0;
    height: 0;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-bottom: 8px solid rgb(255, 255, 255);
}

.login-dropdown:hover .dropdown-content {
    display: block;
    opacity: 1;
    transform: translateY(0);
}

.dropdown-content a {
    color: #000000;
    padding: 0.75rem 1.25rem;
    text-decoration: none;
    display: block;
    font-weight: 500;
    font-size: 0.9rem;
    transition: all 0.2s ease;
    border-radius: 0;
}

.dropdown-content a:hover {
    background: #832729;
    color: rgb(255, 255, 255);
    transform: translateX(5px);
}

.dropdown-content a:first-child {
    border-radius: 12px 12px 0 0;
}

.dropdown-content a:last-child {
    border-radius: 0 0 12px 12px;
}

/* Removed mobile menu styles - using direct icon display instead */

/* Responsive design */
@media (max-width: 1024px) {
    .navigation {
        padding: 1rem 1.5rem;
    }

    .search-bar {
        max-width: 450px;
    }
}

@media (max-width: 768px) {
    .navigation {
        flex-direction: column;
        align-items: stretch;
        padding: 1.5rem 1rem;
        gap: 1rem;
        min-height: 100px;
        background: rgb(255, 255, 255);
    }

    /* Top row: Logo left, Custom images right */
    .navigation::before {
        content: '';
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        order: 1;
    }

    .logo {
        order: 1;
        align-self: flex-start;
        position: absolute;
        top: 1rem;
        left: 1rem;
        z-index: 10;
    }

    .custom-image {
        order: 2;
        position: absolute;
        top: 1rem;
        right: 1rem;
        display: flex;
        gap: 0.5rem;
        z-index: 10;
    }

    .custom-image img {
        width: 28px;
        height: 28px;
        min-width: 36px;
        min-height: 36px;
    }

    /* Search bar below */
    .search-bar {
        order: 3;
        margin-top: 3rem; /* Space for top row */
        max-width: 100%;
        margin-left: 0;
        margin-right: 0;
        height: 45px;
    }

    .search-bar input {
        padding: 0.75rem 1rem;
        font-size: 1rem;
    }

    /* When scrolled, search bar becomes floating icon - Mobile */
    .search-bar.scrolled {
        position: fixed;
        top: 1rem;
        right: 4rem; /* Position closer to custom images */
        margin-top: 0;
        z-index: 1003;
        width: 32px !important;
        height: 32px !important;
        max-width: 32px !important;
        background: transparent !important;
        border: none !important;
        box-shadow: none !important;
        backdrop-filter: none !important;
        padding: 0 !important;
    }

    .search-bar.scrolled img {
        width: 28px !important;
        height: 28px !important;
        opacity: 0.9;
        filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
    }

    .search-bar.scrolled:hover {
        box-shadow: none !important;
    }

    .search-bar.scrolled:hover img {
        opacity: 1;
        transform: scale(1.1);
    }

    /* Login dropdown positioning */
    .login-dropdown {
        margin-left: 0;
        padding-left: 0;
        border-left: none;
        position: relative;
    }

    .dropdown-content {
        right: 0;
        left: auto;
        transform: translateY(-10px);
        min-width: 180px;
    }

    .login-dropdown:hover .dropdown-content {
        transform: translateY(0);
    }

    .dropdown-content::before {
        right: 20px;
        left: auto;
        transform: none;
    }
}

@media (max-width: 480px) {
    .navigation {
        padding: 0.75rem;
        gap: 0.5rem;
    }

    .logo {
        top: 0.75rem;
        left: 0.75rem;
    }

    .custom-image {
        top: 0.75rem;
        right: 0.75rem;
        gap: 0.4rem;
    }

    .custom-image img {
        width: 24px;
        height: 24px;
        min-width: 32px;
        min-height: 32px;
    }

    .search-bar {
        height: 42px;
        padding: 0.3rem;
        margin-top: 2.5rem;
        max-width: 100%;
    }

    .search-bar input {
        padding: 0.6rem 0.8rem;
        font-size: 0.9rem;
    }

    .search-bar img {
        width: 18px;
        height: 18px;
    }

    /* Mobile scrolled search icon positioning */
    .search-bar.scrolled {
        right: 3.5rem !important;
        width: 28px !important;
        height: 28px !important;
    }

    .search-bar.scrolled img {
        width: 24px !important;
        height: 24px !important;
        margin-right: 13.5rem;
    }

    .dropdown-content {
        min-width: 160px;
    }

    .dropdown-content a {
        font-size: 0.85rem;
        padding: 0.6rem 1rem;
    }
}

@media (max-width: 360px) {
    .navigation {
        padding: 0.5rem;
    }

    .logo {
        top: 0.5rem;
        left: 0.5rem;
    }

    .custom-image {
        top: 0.5rem;
        right: 0.5rem;
        gap: 0.3rem;
    }

    .custom-image img {
        width: 20px;
        height: 20px;
        min-width: 28px;
        min-height: 28px;
    }

    .search-bar {
        height: 40px;
        margin-top: 2rem;
        max-width: 100%;
    }

    .search-bar input {
        padding: 0.5rem 0.7rem;
        font-size: 0.85rem;
    }

    .search-bar img {
        width: 16px;
        height: 16px;
    }

    /* Smallest mobile scrolled search icon */
    .search-bar.scrolled {
        right: 3rem !important;
        width: 26px !important;
        height: 26px !important;
    }

    .search-bar.scrolled img {
        width: 22px !important;
        height: 22px !important;
    }

    .dropdown-content {
        min-width: 140px;
    }

    .dropdown-content a {
        font-size: 0.8rem;
        padding: 0.5rem 0.8rem;
    }
}

/* Category Menu Styles */
.menu-container {
    position: relative;
    background: rgb(255, 255, 255);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
    padding: 1rem;
    background-color: #f2f2f2;
    border-radius: 0;
    width: fit-content;
    margin: 0 auto;
}

.hamburger span {
    width: 25px;
    height: 3px;
    background-color: #333;
    margin: 3px 0;
    transition: 0.3s;
    border-radius: 2px;
}

.hamburger.active span:nth-child(1) {
    transform: rotate(-45deg) translate(-5px, 6px);
}

.hamburger.active span:nth-child(2) {
    opacity: 0;
}

.hamburger.active span:nth-child(3) {
    transform: rotate(45deg) translate(-5px, -6px);
}

.category-menu {
    display: flex;
    flex-wrap: wrap;
    gap: 1.5rem;
    padding: 1rem;
    background-color: #ffffff;
    justify-content: center;
    margin: 0;
    flex-direction: row;
}

.menu-item {
    display: flex;
    align-items: center;
    text-decoration: none;
    color: #000000;
    font-size: 1.2rem;
    font-weight: 500;
    transition: all 0.3s ease;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    position: relative;
    gap: 0.5rem;
}

.menu-item img {
    width: 24px;
    height: 24px;
    transition: transform 0.3s ease;
}

.menu-item .chevron {
    display: none;
    width: 20px;
    height: 20px;
    margin-left: auto;
}

/* Desktop specific styles */
@media (min-width: 769px) {
    .hamburger {
        display: none !important;
    }

    .category-menu {
        display: flex !important;
        flex-direction: row !important;
        position: static !important;
        width: auto !important;
        height: auto !important;
        background-color: #ffffff !important;
        padding: 1rem !important;
        margin: 0 !important;
        overflow: visible !important;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1) !important;
    }

    .menu-content {
        display: contents !important;
        background: none !important;
        margin: 0 !important;
        border-radius: 0 !important;
        box-shadow: none !important;
        max-height: none !important;
        width: auto !important;
        overflow: visible !important;
    }

    .menu-item {
        padding: 0.5rem 1rem !important;
        border: none !important;
        border-radius: 8px !important;
        background: transparent !important;
        margin: 0 !important;
        width: auto !important;
        flex-direction: row !important;
        gap: 0.5rem !important;
    }

    .menu-item .chevron {
        display: none !important;
    }

    .menu-item::after {
        content: '' !important;
        position: absolute !important;
        bottom: 0 !important;
        left: 50% !important;
        width: 0 !important;
        height: 2px !important;
        background-color: #832729 !important;
        transition: all 0.3s ease !important;
        transform: translateX(-50%) !important;
        display: block !important;
    }

    .menu-item:hover::after {
        width: 80% !important;
    }

    .menu-item:hover {
        background-color: transparent !important;
        padding-left: 1rem !important;
        transform: none !important;
    }
}

.menu-item:hover {
    color: #832729;
}

.menu-item:hover img {
    transform: scale(1.1);
}

.menu-item::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background-color: #832729;
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

.menu-item:hover::after {
    width: 80%;
}

.menu-item:active {
    transform: translateY(0);
}

/* Mobile Category Menu Styles */
@media (max-width: 768px) {
    .menu-container {
        position: relative;
    }

    .hamburger {
        display: flex;
        position: fixed;
        top: 1rem;
        left: 1rem;
        z-index: 1005;
        padding: 0.5rem;
        background-color: rgb(255, 255, 255);
        border-radius: 4px;
        width: auto;
        margin: 0;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .hamburger span {
        width: 18px;
        height: 2px;
        background-color: #000000;
        margin: 2px 0;
        transition: 0.3s;
        border-radius: 1px;
    }

    .hamburger.active span:nth-child(1) {
        transform: rotate(-45deg) translate(-3px, 4px);
    }

    .hamburger.active span:nth-child(3) {
        transform: rotate(45deg) translate(-3px, -4px);
    }

    /* Adjust logo positioning to make room for hamburger */
    .logo {
        left: 3.5rem !important; /* Move logo to the right to make space for hamburger */
    }

    .category-menu {
        display: none;
        flex-direction: column;
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        background: linear-gradient(to bottom,
            rgba(0, 0, 0, 0.3) 0%,
            rgba(0, 0, 0, 0.5) 20%,
            rgba(0, 0, 0, 0.5) 80%,
            rgba(0, 0, 0, 0.974) 100%);
        z-index: 1004;
        gap: 0;
        padding: 0;
        overflow-y: auto;
        overflow-x: hidden;
        -webkit-overflow-scrolling: touch;
        scrollbar-width: none;
        -ms-overflow-style: none;
    }

    .category-menu::-webkit-scrollbar {
        display: none;
    }

    .category-menu.active {
        display: flex;
    }

    .menu-content {
        background-color: #ffffff;
        margin: 6vh auto 16vh auto;
        border-radius: 20px;
        overflow-y: auto;
        overflow-x: hidden;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        max-height: 80vh;
        width: 85%;
        max-width: 400px;
        -webkit-overflow-scrolling: touch;
        scrollbar-width: none;
        -ms-overflow-style: none;
    }

    .menu-content::-webkit-scrollbar {
        display: none;
    }

    .menu-item {
        padding: 1.5rem 2rem;
        border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        justify-content: flex-start;
        gap: 1rem;
        border-radius: 0;
        background-color: transparent;
        margin: 0;
        width: 100%;
        box-sizing: border-box;
        transition: all 0.3s ease;
        color: #000000;
        font-weight: 500;
    }

    .menu-item:first-child {
        border-top: none;
        border-radius: 20px 20px 0 0;
    }

    .menu-item:last-child {
        border-bottom: none;
        border-radius: 0 0 20px 20px;
    }

    .menu-item .chevron {
        display: block;
        opacity: 0.7;
        transition: all 0.3s ease;
    }

    .menu-item::after {
        display: none;
    }

    .menu-item:hover {
        background-color: rgba(247, 184, 26, 0.27);
        transform: none;
        padding-left: 2.5rem;
    }

    .menu-item:hover .chevron {
        opacity: 1;
        transform: translateX(5px);
    }

    .menu-item:active {
        background-color: rgba(131, 39, 41, 0.2);
        transform: scale(0.98);
    }
}

@media (max-width: 480px) {
    .hamburger {
        top: 0.75rem;
        left: 0.75rem;
        padding: 0.4rem;
    }

    .hamburger span {
        width: 16px;
        height: 2px;
        margin: 1.5px 0;
    }

    .hamburger.active span:nth-child(1) {
        transform: rotate(-45deg) translate(-2px, 3px);
    }

    .hamburger.active span:nth-child(3) {
        transform: rotate(45deg) translate(-2px, -3px);
    }

    /* Adjust logo positioning for smaller screens */
    .logo {
        left: 3rem !important;
    }
}

@media (max-width: 360px) {
    .hamburger {
        top: 0.5rem;
        left: 0.5rem;
        padding: 0.3rem;
    }

    .hamburger span {
        width: 14px;
        height: 1.5px;
        margin: 1px 0;
    }

    .hamburger.active span:nth-child(1) {
        transform: rotate(-45deg) translate(-2px, 2px);
    }

    .hamburger.active span:nth-child(3) {
        transform: rotate(45deg) translate(-2px, -2px);
    }

    /* Adjust logo positioning for smallest screens */
    .logo {
        left: 2.5rem !important;
    }
}
